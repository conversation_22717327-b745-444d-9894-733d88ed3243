"use client";

import type { SVGProps } from "react";

const PayPalIcon = (props: SVGProps<SVGSVGElement>) => {
    return (
        <svg width="34" height="24" viewBox="0 0 34 24" fill="none" {...props}>
            <path
                d="M0.5 4C0.5 2.067 2.067 0.5 4 0.5H30C31.933 0.5 33.5 2.067 33.5 4V20C33.5 21.933 31.933 23.5 30 23.5H4C2.067 23.5 0.5 21.933 0.5 20V4Z"
                fill="white"
            />
            <path
                d="M0.5 4C0.5 2.067 2.067 0.5 4 0.5H30C31.933 0.5 33.5 2.067 33.5 4V20C33.5 21.933 31.933 23.5 30 23.5H4C2.067 23.5 0.5 21.933 0.5 20V4Z"
                className="stroke-border-secondary"
                strokeWidth="0.75"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M14.614 18.4483L14.8347 16.9992L14.3431 16.9873H11.9951L13.6268 6.2937C13.6319 6.26132 13.6484 6.23126 13.6724 6.20987C13.6965 6.18849 13.7272 6.17676 13.7594 6.17676H17.7184C19.0328 6.17676 19.9398 6.45939 20.4133 7.01734C20.6353 7.27908 20.7767 7.55267 20.8452 7.85364C20.9169 8.16951 20.9181 8.54685 20.8481 9.00715L20.843 9.04063V9.33561L21.0651 9.46563C21.252 9.56815 21.4006 9.68546 21.5145 9.81975C21.7044 10.0436 21.8272 10.3281 21.8791 10.6652C21.9328 11.012 21.9151 11.4248 21.8272 11.892C21.7259 12.4295 21.5622 12.8976 21.341 13.2805C21.1376 13.6334 20.8785 13.9262 20.5708 14.153C20.277 14.3686 19.928 14.5322 19.5333 14.6369C19.1509 14.7398 18.7149 14.7917 18.2367 14.7917H17.9286C17.7083 14.7917 17.4943 14.8737 17.3263 15.0207C17.1579 15.1708 17.0465 15.3758 17.0123 15.6L16.989 15.7305L16.599 18.2848L16.5814 18.3785C16.5767 18.4082 16.5686 18.423 16.5568 18.433C16.5463 18.4422 16.5311 18.4483 16.5164 18.4483H14.614Z"
                fill="#28356A"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M21.2761 9.07458C21.2644 9.15267 21.2508 9.23246 21.2356 9.31445C20.7136 12.0851 18.9273 13.0422 16.646 13.0422H15.4845C15.2055 13.0422 14.9703 13.2516 14.9269 13.536L14.1638 18.5393C14.1356 18.7261 14.2748 18.8944 14.4571 18.8944H16.5173C16.7612 18.8944 16.9684 18.7112 17.0069 18.4626L17.0271 18.3544L17.415 15.8102L17.4399 15.6707C17.4779 15.4211 17.6856 15.2378 17.9295 15.2378H18.2376C20.2336 15.2378 21.7961 14.4003 22.2528 11.9765C22.4435 10.964 22.3448 10.1185 21.84 9.52389C21.6873 9.34464 21.4977 9.1958 21.2761 9.07458Z"
                fill="#298FC2"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M20.7298 8.84956C20.65 8.82549 20.5677 8.80374 20.4833 8.78407C20.3984 8.76488 20.3115 8.7479 20.222 8.73299C19.9089 8.68069 19.5656 8.65588 19.1981 8.65588H16.0951C16.0186 8.65588 15.946 8.67372 15.8811 8.70598C15.7379 8.7771 15.6316 8.91714 15.6058 9.08857L14.9457 13.4101L14.9268 13.5361C14.9701 13.2516 15.2053 13.0423 15.4843 13.0423H16.6459C18.9271 13.0423 20.7134 12.0847 21.2354 9.31451C21.2511 9.23252 21.2642 9.15273 21.2759 9.07464C21.1438 9.00218 21.0008 8.94023 20.8467 8.88744C20.8087 8.87437 20.7694 8.86178 20.7298 8.84956Z"
                fill="#22284F"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M15.6056 9.08862C15.6314 8.91718 15.7377 8.77715 15.8809 8.70652C15.9462 8.67414 16.0184 8.6563 16.0948 8.6563H19.1979C19.5654 8.6563 19.9086 8.68123 20.2218 8.73353C20.3113 8.74831 20.3982 8.76542 20.4831 8.7846C20.5675 8.80415 20.6498 8.82603 20.7296 8.84998C20.7692 8.8622 20.8085 8.8749 20.8469 8.88749C21.0009 8.94028 21.1441 9.00272 21.2761 9.07469C21.4315 8.05082 21.2748 7.3537 20.7393 6.72245C20.1488 6.0274 19.0831 5.72998 17.7194 5.72998H13.7603C13.4817 5.72998 13.2441 5.9393 13.2011 6.22426L11.5521 17.0279C11.5196 17.2416 11.679 17.4344 11.8876 17.4344H14.3318L15.6056 9.08862Z"
                fill="#28356A"
            />
        </svg>
    );
};

export default PayPalIcon;
