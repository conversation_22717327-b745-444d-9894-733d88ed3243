import type { SVGProps } from "react";

interface Props extends SVGProps<SVGSVGElement> {
    size?: number;
}

const Discord = ({ size = 24, ...props }: Props) => {
    return (
        <svg width={size} height={size} viewBox="0 0 24 24" fill="none" {...props}>
            <path
                d="M7.50197 13.0057C7.50197 14.0114 8.28047 14.8343 9.14548 14.8343C10.0105 14.8343 10.789 14.0114 10.789 13.0057C10.789 12 10.0105 11.1771 9.14548 11.1771C8.28047 11.1771 7.50197 12 7.50197 13.0057Z"
                fill="currentColor"
            />
            <path
                d="M13.211 13.0057C13.211 14.0114 13.9895 14.8343 14.8545 14.8343C15.7195 14.8343 16.498 14.0114 16.498 13.0057C16.498 12 15.7195 11.1771 14.8545 11.1771C13.9895 11.1771 13.211 12 13.211 13.0057Z"
                fill="currentColor"
            />
            <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M0.560538 2.80805C0 3.90817 0 5.3483 0 8.22857V15.7714C0 18.6517 0 20.0918 0.560538 21.192C1.0536 22.1596 1.84036 22.9464 2.80805 23.4395C3.90817 24 5.3483 24 8.22857 24H15.7714C18.6517 24 20.0918 24 21.192 23.4395C22.1596 22.9464 22.9464 22.1596 23.4395 21.192C24 20.0918 24 18.6517 24 15.7714V8.22857C24 5.3483 24 3.90817 23.4395 2.80805C22.9464 1.84036 22.1596 1.0536 21.192 0.560538C20.0918 0 18.6517 0 15.7714 0H8.22857C5.3483 0 3.90817 0 2.80805 0.560538C1.84036 1.0536 1.0536 1.84036 0.560538 2.80805ZM14.595 5.14286C16.2385 5.23429 17.7955 5.78286 19.0931 6.88C20.5636 9.80572 21.3421 13.0971 21.4286 16.48C20.1311 17.9429 18.3145 18.8571 16.4115 18.8571C16.4115 18.8571 15.806 18.1257 15.3735 17.4857C16.498 17.2114 17.536 16.5714 18.228 15.5657C17.6225 15.9314 17.017 16.2971 16.4115 16.5714C15.633 16.9371 14.8545 17.12 14.076 17.3029C13.384 17.3943 12.692 17.4857 12 17.4857C11.308 17.4857 10.616 17.3943 9.92398 17.3029C9.14548 17.12 8.36697 16.9371 7.58847 16.5714C6.98296 16.2971 6.37746 15.9314 5.77195 15.5657C6.46396 16.5714 7.50197 17.2114 8.62647 17.4857C8.19397 18.1257 7.58847 18.8571 7.58847 18.8571C5.68545 18.8571 3.86894 17.9429 2.57143 16.48C2.65793 13.0971 3.43644 9.80572 4.90695 6.88C6.20446 5.78286 7.76147 5.23429 9.40498 5.14286L9.66448 5.41714C8.19397 5.78286 6.89646 6.51429 5.68545 7.52C7.15596 6.69714 8.79948 6.14857 10.5295 5.96571C11.0485 5.87429 11.481 5.87429 12 5.87429C12.519 5.87429 12.9515 5.87429 13.4705 5.96571C15.2005 6.14857 16.844 6.69714 18.3145 7.52C17.1035 6.51429 15.806 5.78286 14.3355 5.41714L14.595 5.14286Z"
                fill="currentColor"
            />
        </svg>
    );
};

export default Discord;
