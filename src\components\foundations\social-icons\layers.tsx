import type { SVGProps } from "react";

interface Props extends SVGProps<SVGSVGElement> {
    size?: number;
}

const Layers = ({ size = 24, ...props }: Props) => {
    return (
        <svg width={size} height={size} viewBox="0 0 24 24" fill="none" {...props}>
            <g clipPath="url(#clip0_7441_94320)">
                <path
                    opacity="0.2"
                    d="M0.363357 13.6945L0.421539 4.96865C0.431721 3.38902 1.53645 2.03484 3.06154 1.73302L11.4884 0.0624719C13.5132 -0.338983 15.3924 1.24574 15.3794 3.34247L15.3212 12.0697C15.3103 13.6487 14.2055 15.0021 12.6804 15.3047L4.25354 16.9752C2.22881 17.3767 0.349539 15.7919 0.363357 13.6945Z"
                    fill="currentColor"
                />
                <path
                    opacity="0.5"
                    d="M4.49226 17.1762L4.55044 8.44892C4.5599 6.87073 5.66535 5.51728 7.19045 5.21473L15.6174 3.54492C17.6421 3.14346 19.5214 4.72746 19.5075 6.82492L19.4494 15.5522C19.4392 17.1311 18.3344 18.4846 16.8094 18.7871L8.38245 20.4576C6.35772 20.8584 4.47845 19.2744 4.49226 17.1776V17.1762Z"
                    fill="currentColor"
                />
                <path
                    opacity="0.8"
                    d="M8.62091 20.6576L8.67909 11.9303C8.68928 10.3507 9.794 8.99797 11.3191 8.69542L19.746 7.02487C21.7707 6.62342 23.65 8.20815 23.6362 10.3049L23.578 19.0321C23.5678 20.6118 22.4631 21.9652 20.938 22.2671L12.5111 23.9376C10.4864 24.3391 8.60709 22.7543 8.62019 20.6576H8.62091Z"
                    fill="currentColor"
                />
            </g>
            <defs>
                <clipPath id="clip0_7441_94320">
                    <rect width="24" height="24" fill="white" />
                </clipPath>
            </defs>
        </svg>
    );
};

export default Layers;
