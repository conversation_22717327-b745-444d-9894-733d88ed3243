"use client";

import type { SVGProps } from "react";
import { cx } from "@/utils/cx";

export const Square = (props: Omit<SVGProps<SVGSVGElement>, "size"> & { size?: "sm" | "md" | "lg" }) => {
    const { size = "lg", className } = props;
    const Pattern = sizes[size];
    return <Pattern className={className} />;
};

const lg = (props: SVGProps<SVGSVGElement>) => {
    return (
        <svg width="910" height="910" viewBox="0 0 910 910" fill="none" {...props} className={cx("text-border-secondary", props.className)}>
            <mask id="mask0_16747_25726" style={{ maskType: "alpha" }} maskUnits="userSpaceOnUse" x="-4" y="-4" width="918" height="918">
                <path
                    d="M1.31903 182.741C-1.0717 171.957 5.73229 161.277 16.5162 158.886L727.26 1.31801C738.044 -1.07271 748.724 5.73127 751.114 16.5151L908.682 727.259C911.073 738.042 904.269 748.723 893.485 751.113L182.742 908.681C171.958 911.072 161.278 904.268 158.887 893.484L1.31903 182.741Z"
                    fill="url(#paint0_radial_16747_25726)"
                />
            </mask>
            <g mask="url(#mask0_16747_25726)">
                <path
                    d="M417.382 414.687L472.055 402.566C482.569 400.235 492.982 406.869 495.313 417.383L507.434 472.056C509.765 482.57 503.131 492.983 492.617 495.314L437.944 507.435C427.43 509.766 417.017 503.132 414.686 492.618L402.565 437.945C400.234 427.431 406.868 417.018 417.382 414.687Z"
                    stroke="currentColor"
                />
                <path
                    d="M360.132 378.213L508.529 345.314C519.043 342.983 529.457 349.617 531.788 360.131L564.686 508.528C567.017 519.043 560.383 529.456 549.869 531.787L401.472 564.686C390.958 567.017 380.545 560.383 378.214 549.868L345.315 401.471C342.984 390.957 349.618 380.544 360.132 378.213Z"
                    stroke="currentColor"
                />
                <path
                    d="M302.88 341.74L545.002 288.063C555.516 285.732 565.929 292.366 568.26 302.881L621.937 545.002C624.268 555.516 617.634 565.929 607.12 568.26L364.998 621.937C354.484 624.268 344.071 617.634 341.74 607.12L288.063 364.999C285.732 354.484 292.366 344.071 302.88 341.74Z"
                    stroke="currentColor"
                />
                <path
                    d="M245.628 305.267L581.474 230.812C591.988 228.481 602.401 235.114 604.732 245.629L679.188 581.475C681.519 591.989 674.885 602.402 664.37 604.733L328.525 679.188C318.01 681.519 307.597 674.885 305.266 664.371L230.811 328.525C228.48 318.011 235.114 307.598 245.628 305.267Z"
                    stroke="currentColor"
                />
                <path
                    d="M188.378 268.794L617.949 173.561C628.463 171.23 638.876 177.864 641.207 188.378L736.44 617.948C738.771 628.462 732.137 638.876 721.623 641.206L292.053 736.44C281.539 738.771 271.125 732.137 268.795 721.623L173.561 292.052C171.23 281.538 177.864 271.125 188.378 268.794Z"
                    stroke="currentColor"
                />
                <path
                    d="M131.126 232.321L654.421 116.309C664.935 113.978 675.348 120.612 677.679 131.126L793.691 654.421C796.022 664.935 789.388 675.348 778.874 677.679L255.579 793.691C245.065 796.022 234.652 789.388 232.321 778.874L116.309 255.579C113.978 245.065 120.612 234.652 131.126 232.321Z"
                    stroke="currentColor"
                />
                <path
                    d="M73.8744 195.848L690.893 59.058C701.408 56.727 711.821 63.3609 714.152 73.8752L850.942 690.894C853.273 701.409 846.639 711.822 836.124 714.153L219.105 850.942C208.591 853.273 198.178 846.64 195.847 836.125L59.0572 219.106C56.7262 208.592 63.3601 198.179 73.8744 195.848Z"
                    stroke="currentColor"
                />
                <path
                    d="M16.6244 159.374L727.368 1.80616C737.882 -0.524797 748.295 6.10908 750.626 16.6234L908.194 727.367C910.525 737.881 903.891 748.294 893.377 750.625L182.634 908.193C172.119 910.524 161.706 903.89 159.375 893.376L1.80718 182.633C-0.52378 172.118 6.1101 161.705 16.6244 159.374Z"
                    stroke="currentColor"
                />
            </g>
            <defs>
                <radialGradient
                    id="paint0_radial_16747_25726"
                    cx="0"
                    cy="0"
                    r="1"
                    gradientUnits="userSpaceOnUse"
                    gradientTransform="translate(455.001 455) rotate(77.5) scale(384 384)"
                >
                    <stop />
                    <stop offset="1" stopOpacity="0" />
                </radialGradient>
            </defs>
        </svg>
    );
};

const md = (props: SVGProps<SVGSVGElement>) => {
    return (
        <svg width="480" height="480" viewBox="0 0 480 480" fill="none" {...props} className={cx("text-border-secondary", props.className)}>
            <mask id="mask0_4933_421799" style={{ maskType: "alpha" }} maskUnits="userSpaceOnUse" x="0" y="0" width="480" height="480">
                <path
                    d="M0 12C0 5.37257 5.37258 0 12 0H468C474.627 0 480 5.37258 480 12V468C480 474.627 474.627 480 468 480H12C5.37257 480 0 474.627 0 468V12Z"
                    fill="url(#paint0_radial_4933_421799)"
                />
            </mask>
            <g mask="url(#mask0_4933_421799)">
                <path
                    d="M185.833 215.135C184.459 208.934 188.371 202.793 194.572 201.418L264.865 185.835C271.066 184.46 277.207 188.372 278.582 194.573L294.165 264.866C295.54 271.067 291.628 277.208 285.427 278.583L215.134 294.166C208.933 295.541 202.792 291.629 201.417 285.428L185.833 215.135Z"
                    stroke="currentColor"
                />
                <path
                    d="M147.667 190.819C146.293 184.619 150.205 178.477 156.406 177.103L289.182 147.667C295.383 146.292 301.524 150.205 302.899 156.405L332.334 289.182C333.709 295.382 329.797 301.523 323.596 302.898L190.82 332.334C184.619 333.709 178.478 329.796 177.103 323.596L147.667 190.819Z"
                    stroke="currentColor"
                />
                <path
                    d="M109.499 166.504C108.125 160.303 112.037 154.162 118.238 152.787L313.497 109.499C319.698 108.125 325.839 112.037 327.213 118.238L370.501 313.497C371.876 319.698 367.964 325.839 361.763 327.213L166.504 370.501C160.303 371.876 154.162 367.964 152.787 361.763L109.499 166.504Z"
                    stroke="currentColor"
                />
                <path
                    d="M71.3315 142.188C69.9569 135.988 73.8691 129.847 80.0699 128.472L337.812 71.3319C344.013 69.9572 350.154 73.8695 351.528 80.0702L408.669 337.812C410.043 344.013 406.131 350.154 399.93 351.529L142.188 408.669C135.987 410.044 129.846 406.131 128.472 399.931L71.3315 142.188Z"
                    stroke="currentColor"
                />
                <path
                    d="M33.1636 117.873C31.7889 111.672 35.7012 105.531 41.9019 104.156L362.127 33.1643C368.328 31.7896 374.469 35.7019 375.843 41.9027L446.836 362.128C448.21 368.328 444.298 374.47 438.097 375.844L117.872 446.836C111.671 448.211 105.53 444.299 104.156 438.098L33.1636 117.873Z"
                    stroke="currentColor"
                />
                <path
                    d="M-5.00442 93.5575C-6.37909 87.3568 -2.4668 81.2157 3.73393 79.8411L386.442 -5.00326C392.643 -6.37793 398.784 -2.46564 400.158 3.73509L485.003 386.443C486.377 392.644 482.465 398.785 476.264 400.16L93.5564 485.004C87.3556 486.379 81.2146 482.466 79.8399 476.266L-5.00442 93.5575Z"
                    stroke="currentColor"
                />
                <path
                    d="M-43.1704 69.2421C-44.5451 63.0414 -40.6328 56.9003 -34.4321 55.5256L410.759 -43.1708C416.96 -44.5455 423.101 -40.6332 424.475 -34.4325L523.172 410.758C524.546 416.959 520.634 423.1 514.433 424.475L69.2425 523.171C63.0418 524.546 56.9007 520.634 55.526 514.433L-43.1704 69.2421Z"
                    stroke="currentColor"
                />
            </g>
            <defs>
                <radialGradient
                    id="paint0_radial_4933_421799"
                    cx="0"
                    cy="0"
                    r="1"
                    gradientUnits="userSpaceOnUse"
                    gradientTransform="translate(240 240) rotate(90) scale(240 240)"
                >
                    <stop />
                    <stop offset="1" stopOpacity="0" />
                </radialGradient>
            </defs>
        </svg>
    );
};

const sm = (props: SVGProps<SVGSVGElement>) => {
    return (
        <svg width="336" height="336" viewBox="0 0 336 336" fill="none" {...props} className={cx("text-border-secondary", props.className)}>
            <mask id="mask0_4947_375930" style={{ maskType: "alpha" }} maskUnits="userSpaceOnUse" x="0" y="0" width="336" height="336">
                <rect width="336" height="336" fill="url(#paint0_radial_4947_375930)" />
            </mask>
            <g mask="url(#mask0_4947_375930)">
                <path
                    d="M113.833 143.135C112.459 136.934 116.371 130.793 122.572 129.418L192.865 113.835C199.066 112.46 205.207 116.372 206.582 122.573L222.165 192.866C223.54 199.067 219.628 205.208 213.427 206.583L143.134 222.166C136.933 223.541 130.792 219.629 129.417 213.428L113.833 143.135Z"
                    stroke="currentColor"
                />
                <path
                    d="M85.2085 124.898C83.8338 118.697 87.7461 112.556 93.9468 111.181L211.102 85.2084C217.303 83.8337 223.444 87.746 224.819 93.9467L250.792 211.102C252.166 217.303 248.254 223.444 242.053 224.819L124.898 250.791C118.697 252.166 112.556 248.254 111.181 242.053L85.2085 124.898Z"
                    stroke="currentColor"
                />
                <path
                    d="M56.5835 106.661C55.2088 100.46 59.1211 94.3188 65.3218 92.9441L229.34 56.5822C235.54 55.2076 241.681 59.1199 243.056 65.3206L279.418 229.338C280.793 235.539 276.88 241.68 270.68 243.055L106.662 279.417C100.461 280.791 94.32 276.879 92.9453 270.678L56.5835 106.661Z"
                    stroke="currentColor"
                />
                <path
                    d="M27.9594 88.4235C26.5848 82.2228 30.4971 76.0817 36.6978 74.707L247.578 27.9561C253.778 26.5814 259.92 30.4937 261.294 36.6944L308.045 247.574C309.42 253.775 305.508 259.916 299.307 261.291L88.4269 308.042C82.2261 309.416 76.0851 305.504 74.7104 299.303L27.9594 88.4235Z"
                    stroke="currentColor"
                />
                <path
                    d="M-0.665552 70.1864C-2.04022 63.9857 1.87207 57.8446 8.0728 56.47L265.815 -0.670085C272.016 -2.04475 278.157 1.86754 279.531 8.06826L336.671 265.81C338.046 272.011 334.134 278.152 327.933 279.527L70.191 336.667C63.9902 338.042 57.8492 334.129 56.4745 327.929L-0.665552 70.1864Z"
                    stroke="currentColor"
                />
                <path
                    d="M-29.2906 51.9496C-30.6652 45.7489 -26.7529 39.6078 -20.5522 38.2332L284.052 -29.296C290.253 -30.6707 296.394 -26.7584 297.769 -20.5577L365.298 284.047C366.672 290.247 362.76 296.388 356.559 297.763L51.9551 365.292C45.7543 366.667 39.6133 362.755 38.2386 356.554L-29.2906 51.9496Z"
                    stroke="currentColor"
                />
            </g>
            <defs>
                <radialGradient
                    id="paint0_radial_4947_375930"
                    cx="0"
                    cy="0"
                    r="1"
                    gradientUnits="userSpaceOnUse"
                    gradientTransform="translate(168 168) rotate(90) scale(168 168)"
                >
                    <stop />
                    <stop offset="1" stopOpacity="0" />
                </radialGradient>
            </defs>
        </svg>
    );
};

const sizes = {
    sm,
    md,
    lg,
};
