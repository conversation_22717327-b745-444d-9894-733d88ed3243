import { ChevronDown, Container, HelpCircle, LayersTwo01, LogOut01, Settings01, User01 } from "@untitledui/icons";
import { AvatarLabelGroup } from "@/components/base/avatar/avatar-label-group";
import { Button } from "@/components/base/buttons/button";
import { Dropdown } from "@/components/base/dropdown/dropdown";

export const DropdownButton = () => (
    <Dropdown.Root>
        <Button className="group" color="secondary" iconTrailing={ChevronDown}>
            Account
        </Button>

        <Dropdown.Popover>
            <div className="flex gap-3 border-b border-secondary p-3">
                <AvatarLabelGroup
                    size="md"
                    src="https://www.untitledui.com/images/avatars/olivia-rhye?fm=webp&q=80"
                    status="online"
                    title="Olivia Rhye"
                    subtitle="<EMAIL>"
                />
            </div>
            <Dropdown.Menu>
                <Dropdown.Section>
                    <Dropdown.Item addon="⌘K->P" icon={User01}>
                        View profile
                    </Dropdown.Item>
                    <Dropdown.Item addon="⌘S" icon={Settings01}>
                        Settings
                    </Dropdown.Item>
                </Dropdown.Section>
                <Dropdown.Separator />
                <Dropdown.Section>
                    <Dropdown.Item icon={LayersTwo01}>Changelog</Dropdown.Item>
                    <Dropdown.Item icon={HelpCircle}>Support</Dropdown.Item>
                    <Dropdown.Item icon={Container}>API</Dropdown.Item>
                </Dropdown.Section>
                <Dropdown.Separator />
                <Dropdown.Section>
                    <Dropdown.Item addon="⌥⇧Q" icon={LogOut01}>
                        Log out
                    </Dropdown.Item>
                </Dropdown.Section>
            </Dropdown.Menu>
        </Dropdown.Popover>
    </Dropdown.Root>
);
